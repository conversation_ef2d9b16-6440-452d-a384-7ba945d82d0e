from rest_framework import viewsets, status, generics
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework.filters import SearchFilter, OrderingFilter
from django.db.models import Q

from .models import Service, Template
from .serializers import (
    ServiceSerializer,
    ServiceListSerializer,
    ServiceCreateSerializer,
    ServiceUpdateSerializer,
    TemplateSerializer,
    TemplateListSerializer,
    TemplateCreateSerializer,
    TemplateUpdateSerializer,
    TemplateUploadSerializer,
    TemplatePublicSerializer
)
from authentication.permissions import IsAdminUser


class ServiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Service CRUD operations
    Only accessible by Admin users
    Provides full CRUD functionality with filtering and search
    """
    queryset = Service.objects.all()
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['isActive']
    search_fields = ['name', 'toolName']
    ordering_fields = ['name', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == 'list':
            return ServiceListSerializer
        elif self.action == 'create':
            return ServiceCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return ServiceUpdateSerializer
        return ServiceSerializer

    def get_queryset(self):
        """
        Filter queryset based on query parameters
        """
        queryset = Service.objects.all()

        # Filter by active status
        is_active = self.request.query_params.get('isActive', None)
        if is_active is not None:
            queryset = queryset.filter(isActive=is_active.lower() == 'true')

        # Filter by tool name (supports both string and list searches)
        tool_name = self.request.query_params.get('toolName', None)
        if tool_name:
            # Search in both string and JSON array formats
            queryset = queryset.filter(
                Q(toolName__icontains=tool_name) |  # For string toolNames
                Q(toolName__contains=tool_name)     # For JSON array toolNames
            )

        return queryset

    def create(self, request, *args, **kwargs):
        """
        Create a new service
        """
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        service = serializer.save()

        # Return full service data
        response_serializer = ServiceSerializer(service)
        return Response(response_serializer.data, status=status.HTTP_201_CREATED)

    def update(self, request, *args, **kwargs):
        """
        Update a service
        """
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        service = serializer.save()

        # Return full service data
        response_serializer = ServiceSerializer(service)
        return Response(response_serializer.data)

    def destroy(self, request, *args, **kwargs):
        """
        Soft delete a service by setting isActive to False
        """
        instance = self.get_object()
        instance.isActive = False
        instance.save()

        # Return updated service data
        serializer = ServiceSerializer(instance)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=['patch'], url_path='activate')
    def activate(self, request, pk=None):
        """
        Activate a service (set isActive to True)
        """
        service = self.get_object()
        service.isActive = True
        service.save()

        serializer = ServiceSerializer(service)
        return Response(serializer.data)

    @action(detail=True, methods=['patch'], url_path='deactivate')
    def deactivate(self, request, pk=None):
        """
        Deactivate a service (set isActive to False)
        """
        service = self.get_object()
        service.isActive = False
        service.save()

        serializer = ServiceSerializer(service)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='active')
    def active_services(self, request):
        """
        Get only active services
        """
        queryset = self.get_queryset().filter(isActive=True)

        # Apply search and ordering
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='inactive')
    def inactive_services(self, request):
        """
        Get only inactive services
        """
        queryset = self.get_queryset().filter(isActive=False)

        # Apply search and ordering
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='search-by-tool')
    def search_by_tool(self, request):
        """
        Search services by tool name
        """
        tool_name = request.query_params.get('tool', None)
        if not tool_name:
            return Response(
                {'error': 'Tool name parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Search in both string and JSON array formats
        queryset = self.get_queryset().filter(
            Q(toolName__icontains=tool_name) |  # For string toolNames
            Q(toolName__contains=tool_name)     # For JSON array toolNames
        )

        # Apply other filters
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ServiceListSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ServiceListSerializer(queryset, many=True)
        return Response(serializer.data)


# ============================================================================
# TEMPLATE VIEWSET
# ============================================================================

class TemplateViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Template CRUD operations with file upload support
    Only accessible by Admin users
    Provides full CRUD functionality with filtering, search, and file handling
    """
    queryset = Template.objects.select_related('service').all()
    permission_classes = [IsAuthenticated, IsAdminUser]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['isActive', 'service']
    search_fields = ['title', 'description', 'service__name']
    ordering_fields = ['title', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == 'list':
            return TemplateListSerializer
        elif self.action == 'create':
            return TemplateCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return TemplateUpdateSerializer
        elif self.action == 'upload_files':
            return TemplateUploadSerializer
        return TemplateSerializer

    def get_queryset(self):
        """
        Filter queryset based on query parameters
        """
        queryset = Template.objects.select_related('service').all()

        # Filter by active status
        is_active = self.request.query_params.get('isActive', None)
        if is_active is not None:
            queryset = queryset.filter(isActive=is_active.lower() == 'true')

        # Filter by type
        type_id = self.request.query_params.get('type', None)
        if type_id:
            queryset = queryset.filter(type_id=type_id)

        # Filter by service
        service_id = self.request.query_params.get('service', None)
        if service_id:
            queryset = queryset.filter(service_id=service_id)

        # Filter by type name
        type_name = self.request.query_params.get('type_name', None)
        if type_name:
            queryset = queryset.filter(type__name__icontains=type_name)

        # Filter by service name
        service_name = self.request.query_params.get('service_name', None)
        if service_name:
            queryset = queryset.filter(service__name__icontains=service_name)

        return queryset

    def destroy(self, request, *args, **kwargs):
        """
        Soft delete - set isActive to False instead of deleting
        """
        instance = self.get_object()
        instance.isActive = False
        instance.save()

        return Response(
            {'message': f'Template "{instance.title}" has been deactivated'},
            status=status.HTTP_200_OK
        )

    @action(detail=True, methods=['post'], url_path='activate')
    def activate_template(self, request, pk=None):
        """
        Activate a template
        """
        template = self.get_object()
        template.isActive = True
        template.save()

        serializer = TemplateSerializer(template, context={'request': request})
        return Response({
            'message': f'Template "{template.title}" has been activated',
            'template': serializer.data
        })

    @action(detail=True, methods=['post'], url_path='deactivate')
    def deactivate_template(self, request, pk=None):
        """
        Deactivate a template
        """
        template = self.get_object()
        template.isActive = False
        template.save()

        serializer = TemplateSerializer(template, context={'request': request})
        return Response({
            'message': f'Template "{template.title}" has been deactivated',
            'template': serializer.data
        })

    @action(detail=False, methods=['get'], url_path='active')
    def active_templates(self, request):
        """
        Get only active templates
        """
        queryset = self.get_queryset().filter(isActive=True)

        # Apply search and ordering
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = TemplateListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = TemplateListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='inactive')
    def inactive_templates(self, request):
        """
        Get only inactive templates
        """
        queryset = self.get_queryset().filter(isActive=False)

        # Apply search and ordering
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = TemplateListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = TemplateListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], url_path='upload-files')
    def upload_files(self, request, pk=None):
        """
        Upload or update files for a template
        """
        template = self.get_object()
        serializer = TemplateUploadSerializer(template, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()

            # Return updated template data
            template_serializer = TemplateSerializer(template, context={'request': request})
            return Response({
                'message': 'Files uploaded successfully',
                'template': template_serializer.data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], url_path='by-type/(?P<type_id>[^/.]+)')
    def templates_by_type(self, request, type_id=None):
        """
        Get templates by type ID
        """
        try:
            type_obj = Type.objects.get(id=type_id)
        except Type.DoesNotExist:
            return Response(
                {'error': 'Type not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        queryset = self.get_queryset().filter(type=type_obj)
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = TemplateListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = TemplateListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='by-service/(?P<service_id>[^/.]+)')
    def templates_by_service(self, request, service_id=None):
        """
        Get templates by service ID
        """
        try:
            service_obj = Service.objects.get(id=service_id)
        except Service.DoesNotExist:
            return Response(
                {'error': 'Service not found'},
                status=status.HTTP_404_NOT_FOUND
            )

        queryset = self.get_queryset().filter(service=service_obj)
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = TemplateListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = TemplateListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'], url_path='search')
    def search_templates(self, request):
        """
        Advanced search for templates
        """
        query = request.query_params.get('q', '')
        if not query:
            return Response(
                {'error': 'Search query parameter "q" is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Search in title, description, type name, and service name
        queryset = self.get_queryset().filter(
            Q(title__icontains=query) |
            Q(description__icontains=query) |
            Q(type__name__icontains=query) |
            Q(service__name__icontains=query)
        )

        # Apply other filters
        queryset = self.filter_queryset(queryset)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = TemplateListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = TemplateListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)


# ============================================================================
# PUBLIC TEMPLATE VIEW
# ============================================================================

class PublicTemplateListView(generics.ListAPIView):
    """
    Public endpoint for listing all active templates with file and demo video URLs
    No authentication required
    Provides filtering, search, and pagination
    """
    serializer_class = TemplatePublicSerializer
    permission_classes = [AllowAny]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['service']
    search_fields = ['title', 'description', 'service__name']
    ordering_fields = ['title', 'created_at', 'updated_at']
    ordering = ['-created_at']

    def get_queryset(self):
        """
        Return only active templates with their service information
        """
        return Template.objects.select_related('service').filter(isActive=True)

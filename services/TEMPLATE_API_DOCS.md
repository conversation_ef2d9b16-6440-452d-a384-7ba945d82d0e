# Template API Documentation

## Overview

The Template API provides both public and admin endpoints for managing templates with file upload capabilities.

## Public Endpoints

### Public Template List
- **Endpoint**: `GET /api/public/templates/`
- **Authentication**: None required (Public access)
- **Description**: List all active templates with file and demo video URLs
- **Features**: Filtering, search, pagination

## Admin Endpoints

### Template CRUD Operations
- **Authentication**: JWT Token required
- **Permissions**: Admin users only
- **Header**: `Authorization: Bearer YOUR_JWT_TOKEN`
- **Description**: Full CRUD operations for managing templates

---

## TEMPLATE API

### Model Structure

#### Template Model
- `id` (AutoField): Primary key
- `title` (String): Template title
- `description` (Text): Template description
- `file` (FileField): Template file (PDF, DOC, DOCX, TXT, PPT, PPTX - max 200MB)
- `demo_video` (FileField): Demo video (MP4, AVI, MOV, WMV - max 200MB)
- `service` (ForeignKey): Reference to Service model
- `isActive` (Boolean): Whether the template is active (default: True)
- `created_at` (DateTime): Creation timestamp
- `updated_at` (DateTime): Last update timestamp

### File Upload Configuration

#### Supported File Types
- **Documents**: .pdf, .doc, .docx, .txt, .ppt, .pptx
- **Videos**: .mp4, .avi, .mov, .wmv

#### File Size Limits
- **Maximum file size**: 200MB per file
- **Total upload limit**: 200MB per request

#### Upload Paths
- **Template files**: `media/templates/{filename}`
- **Demo videos**: `media/demo_videos/{filename}`

### Endpoints

#### 1. List Templates
**GET** `/api/templates/`

**Query Parameters:**
- `isActive` (boolean): Filter by active status
- `service` (integer): Filter by service ID
- `search` (string): Search in title, description, and service name
- `ordering` (string): Order by fields (title, created_at, updated_at)
- `page` (integer): Page number for pagination

**Response:**
```json
{
  "count": 10,
  "next": "http://localhost:8000/api/templates/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "title": "Invoice Template",
      "description": "Professional invoice template",
      "service": 1,
      "service_name": "Invoice Generator",
      "isActive": true,
      "file_size_mb": 2.5,
      "file_extension": "PDF",
      "created_at": "2023-07-25T10:00:00Z",
      "updated_at": "2023-07-25T10:00:00Z"
    }
  ]
}
```

#### 2. Create Template
**POST** `/api/templates/`

**Content-Type**: `multipart/form-data`

**Form Data:**
- `title` (string): Template title
- `description` (string): Template description
- `file` (file): Template file
- `demo_video` (file, optional): Demo video
- `service` (integer): Service ID
- `isActive` (boolean, optional): Active status (default: true)

**Response (201 Created):**
```json
{
  "id": 1,
  "title": "Invoice Template",
  "description": "Professional invoice template",
  "file": "/media/templates/invoice_template.pdf",
  "demo_video": "/media/demo_videos/invoice_demo.mp4",
  "service": 1,
  "service_name": "Invoice Generator",
  "isActive": true,
  "file_size_mb": 2.5,
  "demo_video_size_mb": 15.2,
  "file_extension": "PDF",
  "demo_video_extension": "MP4",
  "file_url": "http://localhost:8000/media/templates/invoice_template.pdf",
  "demo_video_url": "http://localhost:8000/media/demo_videos/invoice_demo.mp4",
  "created_at": "2023-07-25T10:00:00Z",
  "updated_at": "2023-07-25T10:00:00Z"
}
```

#### 3. Retrieve Template
**GET** `/api/templates/{id}/`

**Response:**
```json
{
  "id": 1,
  "title": "Invoice Template",
  "description": "Professional invoice template",
  "file": "/media/templates/invoice_template.pdf",
  "demo_video": "/media/demo_videos/invoice_demo.mp4",
  "service": 1,
  "service_name": "Invoice Generator",
  "isActive": true,
  "file_size_mb": 2.5,
  "demo_video_size_mb": 15.2,
  "file_extension": "PDF",
  "demo_video_extension": "MP4",
  "file_url": "http://localhost:8000/media/templates/invoice_template.pdf",
  "demo_video_url": "http://localhost:8000/media/demo_videos/invoice_demo.mp4",
  "created_at": "2023-07-25T10:00:00Z",
  "updated_at": "2023-07-25T10:00:00Z"
}
```

#### 4. Update Template
**PUT** `/api/templates/{id}/` or **PATCH** `/api/templates/{id}/`

**Content-Type**: `multipart/form-data`

**Form Data:**
- `title` (string, optional): Template title
- `description` (string, optional): Template description
- `file` (file, optional): New template file
- `demo_video` (file, optional): New demo video
- `service` (integer, optional): Service ID
- `isActive` (boolean, optional): Active status

#### 5. Delete Template (Soft Delete)
**DELETE** `/api/templates/{id}/`

**Response (200 OK):**
```json
{
  "message": "Template \"Invoice Template\" has been deactivated"
}
```

### Custom Actions

#### Activate Template
**POST** `/api/templates/{id}/activate/`

#### Deactivate Template
**POST** `/api/templates/{id}/deactivate/`

#### Active Templates
**GET** `/api/templates/active/`

#### Inactive Templates
**GET** `/api/templates/inactive/`

#### Templates by Service
**GET** `/api/templates/by-service/{service_id}/`

---

## Error Responses

### Validation Errors (400 Bad Request)
```json
{
  "title": ["This field is required."],
  "file": ["File size exceeds 200MB limit."]
}
```

### Authentication Error (401 Unauthorized)
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### Permission Error (403 Forbidden)
```json
{
  "detail": "You do not have permission to perform this action."
}
```

### Not Found (404 Not Found)
```json
{
  "detail": "Not found."
}
```

---

## Usage Examples

### Create Template with cURL
```bash
curl -X POST http://localhost:8000/api/templates/ \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "title=Invoice Template" \
  -F "description=Professional invoice template" \
  -F "service=1" \
  -F "file=@invoice_template.pdf" \
  -F "demo_video=@invoice_demo.mp4"
```

### Filter Active Templates
```bash
curl -X GET "http://localhost:8000/api/templates/?isActive=true" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### Search Templates
```bash
curl -X GET "http://localhost:8000/api/templates/?search=invoice" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

---

## PUBLIC ENDPOINTS

### Public Template List
**GET** `/api/public/templates/`

**Authentication**: None required (Public access)

**Query Parameters:**
- `service` (integer): Filter by service ID
- `search` (string): Search in title, description, and service name
- `ordering` (string): Order by fields (title, created_at, updated_at)
- `page` (integer): Page number for pagination

**Response (200 OK):**
```json
{
  "count": 15,
  "next": "http://localhost:8000/api/public/templates/?page=2",
  "previous": null,
  "results": [
    {
      "id": 1,
      "title": "Invoice Template",
      "description": "Professional invoice template for businesses",
      "service": 1,
      "service_name": "Invoice Generator",
      "file_size_mb": 2.5,
      "demo_video_size_mb": 15.2,
      "file_extension": "PDF",
      "demo_video_extension": "MP4",
      "file_url": "http://localhost:8000/media/templates/invoice_template.pdf",
      "demo_video_url": "http://localhost:8000/media/demo_videos/invoice_demo.mp4",
      "created_at": "2023-07-25T10:00:00Z",
      "updated_at": "2023-07-25T10:00:00Z"
    },
    {
      "id": 2,
      "title": "Contract Template",
      "description": "Legal contract template",
      "service": 2,
      "service_name": "Contract Builder",
      "file_size_mb": 1.8,
      "demo_video_size_mb": null,
      "file_extension": "DOCX",
      "demo_video_extension": null,
      "file_url": "http://localhost:8000/media/templates/contract_template.docx",
      "demo_video_url": null,
      "created_at": "2023-07-24T15:30:00Z",
      "updated_at": "2023-07-24T15:30:00Z"
    }
  ]
}
```

### Usage Examples

#### Get All Public Templates
```bash
curl -X GET "http://localhost:8000/api/public/templates/"
```

#### Filter by Service
```bash
curl -X GET "http://localhost:8000/api/public/templates/?service=1"
```

#### Search Templates
```bash
curl -X GET "http://localhost:8000/api/public/templates/?search=invoice"
```

#### Order by Title
```bash
curl -X GET "http://localhost:8000/api/public/templates/?ordering=title"
```

---

## Notes

- All file uploads are validated for size and type
- Files are stored in organized directory structure
- Soft delete is used to preserve data integrity
- All endpoints support pagination
- Search functionality works across multiple fields
- File URLs are automatically generated for easy access
